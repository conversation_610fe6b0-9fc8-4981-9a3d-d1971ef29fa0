/**
 * Centralized constants for the ProcessCheckpoint PCF Control
 * This file contains all hardcoded values to ensure consistency and maintainability
 */

// ===== UI STYLING CONSTANTS =====

export const UI_COLORS = {
    // Background colors
    WHITE: '#ffffff',
    LIGHT_GRAY: '#f8f8f8',
    MODAL_OVERLAY: 'rgba(0, 0, 0, 0.6)',
    READ_ONLY_INFO_BG: '#f3f2f1',
    
    // Border colors
    DEFAULT_BORDER: '#d1d1d1',
    READ_ONLY_BORDER: '#d2d0ce',
    ACTIVE_BORDER: '#605e5c',
    READ_ONLY_INFO_BORDER: '#8a8886',
    
    // Text colors
    DEFAULT_TEXT: '#323130',
    READ_ONLY_TEXT: '#8a8886',
    SECONDARY_TEXT: '#605e5c',
    
    // Button colors
    REJECT_BUTTON_BG: '#d13438',
    APPROVE_BUTTON_BG: '#107c10',
    BUTTON_TEXT_WHITE: 'white'
} as const;

export const UI_DIMENSIONS = {
    // Component dimensions
    MIN_INPUT_HEIGHT: '32px',
    MODAL_MAX_WIDTH: '800px',
    MODAL_MAX_HEIGHT: '90vh',
    TEXTAREA_MAX_HEIGHT: '60vh',
    
    // Spacing
    MODAL_PADDING: '20px',
    INFO_BAR_PADDING: '8px 12px',
    INFO_BAR_MARGIN_BOTTOM: '12px',
    
    // Border radius
    DEFAULT_BORDER_RADIUS: '4px',
    MODAL_BORDER_RADIUS: '8px',
    INFO_BAR_BORDER_WIDTH: '3px',
    
    // Z-index
    MODAL_Z_INDEX: 999999
} as const;

export const UI_TYPOGRAPHY = {
    // Font families
    DEFAULT_FONT_FAMILY: '"Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif',
    
    // Font sizes
    DEFAULT_FONT_SIZE: '14px',
    MODAL_TITLE_SIZE: '18px',
    INFO_BAR_SIZE: '13px',
    
    // Font weights
    MODAL_TITLE_WEIGHT: '600'
} as const;

export const UI_EFFECTS = {
    // Shadows
    MODAL_SHADOW: '0 8px 32px rgba(0, 0, 0, 0.4)',
    
    // Transitions
    DEFAULT_TRANSITION: 'all 0.2s ease'
} as const;

// ===== TIMING CONSTANTS =====

export const TIMING = {
    // Initialization delays
    TIMELINE_INIT_DELAY: 100, // ms - Delay before initializing timeline repository
    
    // Toast timeouts
    TOAST_ERROR_TIMEOUT: 5000, // ms - Error toast display duration
    TOAST_DEFAULT_TIMEOUT: 3000, // ms - Default toast display duration
    
    // Component defaults
    TEXTAREA_DEFAULT_ROWS: 15
} as const;

// ===== FILE HANDLING CONSTANTS =====

export const FILE_CONSTANTS = {
    // Default file size limits
    DEFAULT_MAX_FILE_SIZE_MB: 10,
    
    // File size calculation
    BYTES_PER_KB: 1024,
    
    // File size precision
    FILE_SIZE_DECIMAL_PLACES: 2
} as const;

// ===== DEFAULT VALUES =====

export const DEFAULT_VALUES = {
    // Component defaults
    ENABLE_FILE_ATTACHMENT: false,
    MAX_FILE_SIZE: FILE_CONSTANTS.DEFAULT_MAX_FILE_SIZE_MB,
    TEXTAREA_ROWS: TIMING.TEXTAREA_DEFAULT_ROWS,
    
    // Entity metadata fallback
    OBJECT_TYPE_CODE_FALLBACK: 0
} as const;

// ===== ID GENERATION PATTERNS =====

export const ID_PATTERNS = {
    TOAST_ID_PREFIX: 'toast-'
} as const;

// ===== VALIDATION CONSTANTS =====

export const VALIDATION = {
    // Stage ID processing
    STAGE_ID_SHORT_LENGTH: 8 // Characters to take from stage ID for subject generation
} as const;
