import * as React from "react";
import type { ChangeEvent, CSSProperties, FC, KeyboardEvent } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    Spinner,
    Textarea
} from "@fluentui/react-components";
import { BpfActionType, DEFAULT_FILE_CONFIG, DEFAULT_VALUES, TIMING, UI_COLORS, UI_DIMENSIONS, UI_EFFECTS, UI_TYPOGRAPHY, type BpfActionResult, type CommentModalProps, type FileAttachmentConfig } from "../types";
import { FileAttachment } from './FileAttachment';
import { useStrings } from '../localization/LanguageContext';

const containerStyle: CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: UI_COLORS.MODAL_OVERLAY,
    zIndex: UI_DIMENSIONS.MODAL_Z_INDEX,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: UI_DIMENSIONS.MODAL_PADDING
};

const inputContainerStyle: CSSProperties = {
    backgroundColor: UI_COLORS.WHITE,
    borderRadius: UI_DIMENSIONS.MODAL_BORDER_RADIUS,
    boxShadow: UI_EFFECTS.MODAL_SHADOW,
    width: '100%',
    maxWidth: UI_DIMENSIONS.MODAL_MAX_WIDTH,
    maxHeight: UI_DIMENSIONS.MODAL_MAX_HEIGHT,
    display: 'flex',
    flexDirection: 'column'
};

const headerStyle: CSSProperties = {
    padding: '20px 24px 16px',
    borderBottom: `1px solid ${UI_COLORS.DEFAULT_BORDER}`
};

const titleStyle: CSSProperties = {
    margin: 0,
    fontSize: UI_TYPOGRAPHY.MODAL_TITLE_SIZE,
    fontWeight: UI_TYPOGRAPHY.MODAL_TITLE_WEIGHT
};

const contentStyle: CSSProperties = {
    padding: '20px 24px',
    flex: 1,
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column'
};

const fieldStyle: CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    flex: 1
};

const textareaStyle: CSSProperties = {
    width: '100%',
    maxHeight: UI_DIMENSIONS.TEXTAREA_MAX_HEIGHT,
    flex: 1
};

const footerStyle: CSSProperties = {
    padding: '16px 24px',
    borderTop: `1px solid ${UI_COLORS.DEFAULT_BORDER}`,
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '8px'
};

export const CommentModal: FC<CommentModalProps> = ({
    open,
    onOpenChange,
    value = "",
    onChange,
    modalTitle,
    textLabel,
    textPlaceHolder,
    bpfActionHandlers,
    buttonConfig,
    isProcessing = false,
    readOnly = false,
    enableFileAttachment = false,
    allowedFileTypes = [],
    maxFileSize = DEFAULT_VALUES.MAX_FILE_SIZE,
    attachedFiles = [],
    onFilesChange
}) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);
    const [processing, setProcessing] = React.useState(false);
    const [loadingAction, setLoadingAction] = React.useState<string | null>(null);

    const strings = useStrings();
    const fileConfig: FileAttachmentConfig = React.useMemo(() => ({
        enabled: enableFileAttachment,
        allowedTypes: allowedFileTypes.length > 0 ? allowedFileTypes : DEFAULT_FILE_CONFIG.allowedTypes,
        maxSizeInMB: maxFileSize > 0 ? maxFileSize : DEFAULT_FILE_CONFIG.maxSizeInMB,
        maxFiles: DEFAULT_FILE_CONFIG.maxFiles
    }), [enableFileAttachment, allowedFileTypes, maxFileSize]);

    React.useEffect(() => {
        if (open && textareaRef.current) {
            textareaRef.current.focus();
        }
    }, [open]);

    const handleTextareaChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
        if (onChange) {
            onChange(event.target.value);
        }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
            onOpenChange(false);
        }
    };

    const handleContentKeyDown = (event: KeyboardEvent) => {
        // Stop propagation to prevent closing the dialog when interacting with content
        event.stopPropagation();
    };

    const handleBpfAction = (actionType: BpfActionType) => {
        if (processing || isProcessing) return;

        setProcessing(true);
        setLoadingAction(actionType);

        if (actionType === BpfActionType.CANCEL) {
            if (bpfActionHandlers?.onCancel) {
                bpfActionHandlers.onCancel();
            } else {
                onOpenChange(false);
            }
            setProcessing(false);
            setLoadingAction(null);
            return;
        }

        let actionPromise: Promise<BpfActionResult> | null = null;

        switch (actionType) {
            case BpfActionType.SUBMIT:
                if (bpfActionHandlers?.onSubmit) {
                    actionPromise = bpfActionHandlers.onSubmit();
                }
                break;

            case BpfActionType.APPROVE:
                if (bpfActionHandlers?.onApprove) {
                    actionPromise = bpfActionHandlers.onApprove();
                }
                break;

            case BpfActionType.REJECT:
                if (bpfActionHandlers?.onReject) {
                    actionPromise = bpfActionHandlers.onReject();
                }
                break;
        }

        if (actionPromise) {
            void actionPromise
                .then((result) => {
                    if (result.success) {
                        onOpenChange(false);
                    }
                    return result;
                })
                .catch(() => {
                    return { success: false };
                })
                .finally(() => {
                    setProcessing(false);
                    setLoadingAction(null);
                });
        } else {
            setProcessing(false);
            setLoadingAction(null);
        }
    };

    if (!open) {
        return null;
    }

    return (
        <div
            onKeyDown={handleKeyDown}
            style={containerStyle}
            tabIndex={-1}
            role="dialog"
            aria-modal="true"
        >
            <div
                style={inputContainerStyle}
                onClick={(e) => e.stopPropagation()}
                onKeyDown={handleContentKeyDown}
                role="document"
            >
                <div style={headerStyle}>
                    <h2 style={titleStyle}>
                        {modalTitle}
                    </h2>
                </div>

                <div style={contentStyle}>
                    {readOnly && (
                        <div style={{
                            backgroundColor: UI_COLORS.READ_ONLY_INFO_BG,
                            borderLeft: `${UI_DIMENSIONS.INFO_BAR_BORDER_WIDTH} solid ${UI_COLORS.READ_ONLY_INFO_BORDER}`,
                            padding: UI_DIMENSIONS.INFO_BAR_PADDING,
                            marginBottom: UI_DIMENSIONS.INFO_BAR_MARGIN_BOTTOM,
                            fontSize: UI_TYPOGRAPHY.INFO_BAR_SIZE,
                            color: UI_COLORS.SECONDARY_TEXT
                        }}>
                            {strings.ui.readOnly.description}
                        </div>
                    )}

                    <Field
                        label={
                            <span style={{ color: readOnly ? UI_COLORS.READ_ONLY_TEXT : UI_COLORS.DEFAULT_TEXT }}>
                                {textLabel}
                            </span>
                        }
                        style={fieldStyle}
                    >
                        <Textarea
                            ref={textareaRef}
                            value={value}
                            onChange={handleTextareaChange}
                            rows={TIMING.TEXTAREA_DEFAULT_ROWS}
                            style={{
                                ...textareaStyle,
                                backgroundColor: readOnly ? UI_COLORS.LIGHT_GRAY : UI_COLORS.WHITE,
                                borderColor: readOnly ? UI_COLORS.READ_ONLY_BORDER : UI_COLORS.ACTIVE_BORDER,
                                cursor: readOnly ? 'not-allowed' : 'text',
                                color: readOnly ? UI_COLORS.READ_ONLY_TEXT : UI_COLORS.DEFAULT_TEXT
                            }}
                            placeholder={readOnly
                                ? (value ? '' : strings.ui.placeholders.readOnlyComment)
                                : textPlaceHolder
                            }
                            readOnly={readOnly}
                            aria-label={textLabel}
                            aria-describedby="textarea-description"
                            aria-required={!readOnly}
                        />
                    </Field>

                    {fileConfig.enabled && (
                        <FileAttachment
                            files={attachedFiles}
                            onFilesChange={onFilesChange ?? (() => {
                                // Default empty handler for file changes
                            })}
                            config={fileConfig}
                            readOnly={readOnly}
                        />
                    )}
                </div>

                <div style={footerStyle}>
                    {buttonConfig?.showCancel !== false && (
                        <Button
                            appearance="secondary"
                            onClick={() => void handleBpfAction(BpfActionType.CANCEL)}
                            disabled={processing || isProcessing}
                        >
                            {strings.ui.buttons.cancel}
                        </Button>
                    )}

                    {buttonConfig?.showReject && (
                        <Button
                            appearance="secondary"
                            onClick={() => void handleBpfAction(BpfActionType.REJECT)}
                            disabled={processing || isProcessing}
                            style={{
                                backgroundColor: UI_COLORS.REJECT_BUTTON_BG,
                                color: UI_COLORS.BUTTON_TEXT_WHITE,
                                border: 'none'
                            }}
                        >
                            {(processing || isProcessing) && loadingAction === BpfActionType.REJECT ?
                                <><Spinner size="tiny" /> {strings.messages.loading.rejecting}</> : strings.ui.buttons.reject}
                        </Button>
                    )}

                    {buttonConfig?.showSubmit && (
                        <Button
                            appearance="primary"
                            onClick={() => void handleBpfAction(BpfActionType.SUBMIT)}
                            disabled={processing || isProcessing}
                        >
                            {(processing || isProcessing) && loadingAction === BpfActionType.SUBMIT ?
                                <><Spinner size="tiny" /> {strings.messages.loading.submitting}</> : strings.ui.buttons.submit}
                        </Button>
                    )}

                    {buttonConfig?.showApprove && (
                        <Button
                            appearance="primary"
                            onClick={() => void handleBpfAction(BpfActionType.APPROVE)}
                            disabled={processing || isProcessing}
                            style={{
                                backgroundColor: UI_COLORS.APPROVE_BUTTON_BG,
                                color: UI_COLORS.BUTTON_TEXT_WHITE,
                                border: 'none'
                            }}
                        >
                            {(processing || isProcessing) && loadingAction === BpfActionType.APPROVE ?
                                <><Spinner size="tiny" /> {strings.messages.loading.approving}</> : strings.ui.buttons.approve}
                        </Button>
                    )}
                </div>
            </div>
        </div>
    );
};