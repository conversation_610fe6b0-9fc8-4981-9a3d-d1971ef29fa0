export const TOAST_INTENTS = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
} as const;

export type ToastIntentType = typeof TOAST_INTENTS[keyof typeof TOAST_INTENTS];
export type ToastIntent = ToastIntentType;

// Legacy enum for backward compatibility
export enum ToastIntentEnum {
    SUCCESS = 'success',
    ERROR = 'error',
    WARNING = 'warning',
    INFO = 'info'
}
