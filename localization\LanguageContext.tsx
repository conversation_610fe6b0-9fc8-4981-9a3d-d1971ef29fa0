import * as React from 'react';
import { getLocalizedStrings, type SupportedLanguage, type LocalizationStrings } from '../hooks/UseLocalization';

interface LanguageContextType {
    strings: LocalizationStrings;
}

const LanguageContext = React.createContext<LanguageContextType>({
    strings: getLocalizedStrings('vi')
});

interface LanguageProviderProps {
    children: React.ReactNode;
    language?: SupportedLanguage;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({
    children,
    language = 'vi'
}) => {
    // Static strings based on PCF property - no state needed
    const strings = React.useMemo(() => {
        return getLocalizedStrings(language);
    }, [language]);

    const contextValue = React.useMemo(() => ({
        strings
    }), [strings]);

    return (
        <LanguageContext.Provider value={contextValue}>
            {children}
        </LanguageContext.Provider>
    );
};

export const useStrings = (): LocalizationStrings => {
    const context = React.useContext(LanguageContext);

    if (!context) {
        return getLocalizedStrings('vi');
    }

    return context.strings;
};