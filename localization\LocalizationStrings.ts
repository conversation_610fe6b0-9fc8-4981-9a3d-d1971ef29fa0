export interface LocalizationStrings {
    // UI Labels
    ui: {
        buttons: {
            cancel: string;
            submit: string;
            recommend: string;    // Tham mưu
            approve: string;      // Duyệt
            reject: string;       // Từ chối
            tryAgain: string;
            attachFile: string;
            removeFile: string;
            downloadFile: string;
        };
        placeholders: {
            defaultInput: string;
            readOnlyComment: string;
            enterComment: string;
            clickToViewComment: string;
            clickToViewCommentEn: string;
            loadingStageData: string;
        };
        titles: {
            defaultPopup: string;
            errorTitle: string;
        };
        labels: {
            defaultLabel: string;
            errorDetails: string;
        };
        readOnly: {
            title: string;
            description: string;
            indicator: string;
        };
        fileAttachment: {
            title: string;
            dragDropText: string;
            browseText: string;
            maxSizeText: string;
            allowedTypesText: string;
            attachedFilesTitle: string;
            noFilesText: string;
            fileSizeUnits: {
                bytes: string;
                kb: string;
                mb: string;
                gb: string;
                zeroBytes: string;
            };
        };
    };

    // Messages
    messages: {
        success: {
            submitted: string;
            recommended: string;   // Tham mưu thành công
            approved: string;      // Phê duyệt thành công
            rejected: string;      // Từ chối thành công
        };
        error: {
            submitFailed: string;
            approveFailed: string;
            rejectFailed: string;
            noteCreationFailed: string;
            operationFailed: string;
            bpfNotAvailable: string;
            fileUploadFailed: string;
            fileSizeExceeded: string;
            fileTypeNotAllowed: string;
        };
        warning: {
            noteCreationWarning: string;
        };
        confirmation: {
            discardChanges: string;
        };
        loading: {
            submitting: string;
            recommending: string;  // Đang tham mưu
            approving: string;     // Đang phê duyệt
            rejecting: string;     // Đang từ chối
        };
    };

    // Error Messages
    errors: {
        general: {
            unknownError: string;
            missingBpfContext: string;
            missingPermissions: string;
        };
        errorBoundary: {
            title: string;
            description: string;
            retryButton: string;
        };
    };
}

// Vietnamese localization (default)
export const vietnameseStrings: LocalizationStrings = {
    ui: {
        buttons: {
            cancel: "Hủy",
            submit: "Gửi",
            recommend: "Tham mưu",
            approve: "Phê duyệt",
            reject: "Từ chối",
            tryAgain: "Thử lại",
            attachFile: "Đính kèm tệp",
            removeFile: "Xóa tệp",
            downloadFile: "Tải xuống"
        },
        placeholders: {
            defaultInput: "Nhập nội dung của bạn tại đây",
            readOnlyComment: "Bình luận trước đó (chỉ đọc)",
            enterComment: "Nhập bình luận của bạn tại đây",
            clickToViewComment: "Click để xem comment (chỉ đọc)",
            clickToViewCommentEn: "Click to view comment (read-only)",
            loadingStageData: "Đang tải dữ liệu stage..."
        },
        titles: {
            defaultPopup: "Checkpoint Quy Trình",
            errorTitle: "Lỗi Process Checkpoint"
        },
        labels: {
            defaultLabel: "Bình luận/Ghi chú:",
            errorDetails: "Chi tiết lỗi"
        },
        readOnly: {
            title: "Chế độ chỉ xem",
            description: "Stage này đã hoàn thành - Chỉ có thể xem comment",
            indicator: "chỉ xem"
        },
        fileAttachment: {
            title: "Đính kèm tệp",
            dragDropText: "Kéo thả một tệp vào đây hoặc",
            browseText: "chọn tệp",
            maxSizeText: "Kích thước tối đa",
            allowedTypesText: "Loại tệp được phép",
            attachedFilesTitle: "Tệp đính kèm",
            noFilesText: "Chưa có tệp nào được đính kèm",
            fileSizeUnits: {
                bytes: "Bytes",
                kb: "KB",
                mb: "MB",
                gb: "GB",
                zeroBytes: "0 Bytes"
            }
        }
    },
    messages: {
        success: {
            submitted: "Đã gửi thành công",
            recommended: "Đã tham mưu và chuyển sang giai đoạn tiếp theo",
            approved: "Đã phê duyệt và chuyển sang giai đoạn tiếp theo",
            rejected: "Đã từ chối và quay về giai đoạn trước"
        },
        error: {
            submitFailed: "Không thể gửi",
            approveFailed: "Không thể phê duyệt và chuyển sang giai đoạn tiếp theo",
            rejectFailed: "Không thể từ chối và quay về giai đoạn trước",
            noteCreationFailed: "Không thể lưu bình luận",
            operationFailed: "Thao tác thất bại",
            bpfNotAvailable: "BPF helper không khả dụng",
            fileUploadFailed: "Không thể tải lên tệp",
            fileSizeExceeded: "Kích thước tệp vượt quá giới hạn cho phép",
            fileTypeNotAllowed: "Loại tệp không được phép"
        },
        warning: {
            noteCreationWarning: "Bình luận có thể chưa được lưu vào timeline"
        },
        confirmation: {
            discardChanges: "Bạn có nội dung chưa lưu. Bạn có chắc muốn hủy không?"
        },
        loading: {
            submitting: "Đang gửi...",
            recommending: "Đang tham mưu...",
            approving: "Đang phê duyệt...",
            rejecting: "Đang từ chối..."
        }
    },
    errors: {
        general: {
            unknownError: "Đã xảy ra lỗi không xác định",
            missingBpfContext: "Thiếu ngữ cảnh BPF",
            missingPermissions: "Thiếu quyền truy cập"
        },
        errorBoundary: {
            title: "Lỗi Process Checkpoint",
            description: "Đã xảy ra lỗi với control Process Checkpoint. Điều này có thể do thiếu ngữ cảnh BPF hoặc quyền truy cập.",
            retryButton: "Thử lại"
        }
    }
};

// English localization
export const englishStrings: LocalizationStrings = {
    ui: {
        buttons: {
            cancel: "Cancel",
            submit: "Submit",
            recommend: "Recommend",
            approve: "Approve",
            reject: "Reject",
            tryAgain: "Try Again",
            attachFile: "Attach File",
            removeFile: "Remove File",
            downloadFile: "Download"
        },
        placeholders: {
            defaultInput: "Enter your content here",
            readOnlyComment: "Previous comment (read-only)",
            enterComment: "Enter your comments here",
            clickToViewComment: "Click to view comment (read-only)",
            clickToViewCommentEn: "Click to view comment (read-only)",
            loadingStageData: "Loading stage data..."
        },
        titles: {
            defaultPopup: "Process Checkpoint",
            errorTitle: "Process Checkpoint Error"
        },
        labels: {
            defaultLabel: "Comments/Notes:",
            errorDetails: "Error Details"
        },
        readOnly: {
            title: "Read-Only Mode",
            description: "This stage is completed - View comment only",
            indicator: "read-only"
        },
        fileAttachment: {
            title: "File Attachment",
            dragDropText: "Drag and drop a file here or",
            browseText: "browse file",
            maxSizeText: "Maximum size",
            allowedTypesText: "Allowed file types",
            attachedFilesTitle: "Attached File",
            noFilesText: "No file attached",
            fileSizeUnits: {
                bytes: "Bytes",
                kb: "KB",
                mb: "MB",
                gb: "GB",
                zeroBytes: "0 Bytes"
            }
        }
    },
    messages: {
        success: {
            submitted: "Submitted successfully",
            recommended: "Recommended and moved to next stage",
            approved: "Approved and moved to next stage",
            rejected: "Rejected and moved to previous stage"
        },
        error: {
            submitFailed: "Failed to submit",
            approveFailed: "Failed to approve and move to next stage",
            rejectFailed: "Failed to reject and move to previous stage",
            noteCreationFailed: "Failed to save comment",
            operationFailed: "Operation failed",
            bpfNotAvailable: "BPF helper is not available",
            fileUploadFailed: "Failed to upload file",
            fileSizeExceeded: "File size exceeds the maximum allowed limit",
            fileTypeNotAllowed: "File type is not allowed"
        },
        warning: {
            noteCreationWarning: "Comment may not have been saved to timeline"
        },
        confirmation: {
            discardChanges: "You have unsaved content. Are you sure you want to cancel?"
        },
        loading: {
            submitting: "Submitting...",
            recommending: "Recommending...",
            approving: "Approving...",
            rejecting: "Rejecting..."
        }
    },
    errors: {
        general: {
            unknownError: "Unknown error occurred",
            missingBpfContext: "Missing BPF context",
            missingPermissions: "Missing permissions"
        },
        errorBoundary: {
            title: "Process Checkpoint Error",
            description: "Something went wrong with the Process Checkpoint control. This might be due to missing BPF context or permissions.",
            retryButton: "Try Again"
        }
    }
};