export type SupportedLanguage = 'vi' | 'en';

export const DEFAULT_LANGUAGE: SupportedLanguage = 'vi';
export const SUPPORTED_LANGUAGES = ['vi', 'en'] as const;

export const isValidLanguage = (lang: string): lang is SupportedLanguage => {
    return SUPPORTED_LANGUAGES.includes(lang as SupportedLanguage);
};

export const getValidatedLanguage = (lang?: string | null): SupportedLanguage => {
    return lang && isValidLanguage(lang) ? lang : DEFAULT_LANGUAGE;
};