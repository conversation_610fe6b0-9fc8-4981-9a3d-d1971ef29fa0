/**
 * Localization and Language Types
 * Centralized location for all language-related type definitions and constants
 */

export type SupportedLanguage = 'vi' | 'en';

export const DEFAULT_LANGUAGE: SupportedLanguage = 'vi';
export const SUPPORTED_LANGUAGES = ['vi', 'en'] as const;

/**
 * Utility type to ensure language validation
 */
export const isValidLanguage = (lang: string): lang is SupportedLanguage => {
    return SUPPORTED_LANGUAGES.includes(lang as SupportedLanguage);
};

/**
 * Get validated language with fallback to default
 */
export const getValidatedLanguage = (lang?: string | null): SupportedLanguage => {
    return lang && isValidLanguage(lang) ? lang : DEFAULT_LANGUAGE;
};
