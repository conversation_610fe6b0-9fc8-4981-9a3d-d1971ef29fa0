import type {
    BpfCollection,
    BpfProcess,
    BpfStage,
    BpfStageInfo,
    EntityInfo,
    IBpfRepository,
    RepositoryOptions,
    RepositoryResult
} from '../types';

interface XrmWindow extends Window {
    Xrm?: {
        Page?: {
            data?: {
                process?: unknown;
                entity?: {
                    getId(): string;
                    getEntityName(): string;
                };
            };
        };
        Utility?: {
            getEntityMetadata(entityName: string): Promise<{
                EntitySetName?: string;
                ObjectTypeCode?: number;
            }>;
        };
    };
}

export class BpfRepository implements IBpfRepository {
    public readonly options: RepositoryOptions;

    constructor(options: RepositoryOptions = {}) {
        this.options = {
            timeout: 5000,
            retryCount: 3,
            ...options
        };
    }



    public getProcessStages(): Promise<RepositoryResult<BpfStageInfo[]>> {
        try {
            const process = this.getBpfProcess();

            if (!process) {
                return Promise.resolve({
                    success: false,
                    error: 'BPF process not available'
                });
            }

            const bpf = process as BpfProcess;
            const collection = bpf.getActivePath();

            if (!collection) {
                return Promise.resolve({
                    success: false,
                    error: 'BPF stages collection not available'
                });
            }

            const stages = this.extractStagesFromCollection(collection);
            const stageInfos = stages.map((stage, index) => ({
                stageId: stage.getId(),
                stageName: stage.getName(),
                isFirstStage: index === 0,
                isLastStage: index === stages.length - 1,
                canMoveNext: index < stages.length - 1,
                canMovePrevious: index > 0
            }));

            return Promise.resolve({
                success: true,
                data: stageInfos
            });
        } catch (error) {
            return Promise.resolve({
                success: false,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    public getCurrentStage(): Promise<RepositoryResult<BpfStageInfo>> {
        try {
            const stageInfo = this.getStageInfo('active');

            if (!stageInfo) {
                return Promise.resolve({
                    success: false,
                    error: 'Current stage not available'
                });
            }

            return Promise.resolve({
                success: true,
                data: stageInfo
            });
        } catch (error) {
            return Promise.resolve({
                success: false,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    public async moveToNextStage(): Promise<RepositoryResult<boolean>> {
        try {
            const process = this.getBpfProcess();

            if (!process) {
                return {
                    success: false,
                    error: 'BPF process not available'
                };
            }

            const stage = this.getStageInfo('active');
            if (!stage?.canMoveNext) {
                return {
                    success: false,
                    error: 'Cannot move to next stage'
                };
            }

            const bpf = process as BpfProcess;
            await bpf.moveNext();

            return {
                success: true,
                data: true
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    public async moveToPreviousStage(): Promise<RepositoryResult<boolean>> {
        try {
            const process = this.getBpfProcess();

            if (!process) {
                return {
                    success: false,
                    error: 'BPF process not available'
                };
            }

            const stage = this.getStageInfo('active');
            if (!stage?.canMovePrevious) {
                return {
                    success: false,
                    error: 'Cannot move to previous stage'
                };
            }

            const bpf = process as BpfProcess;
            await bpf.movePrevious();

            return {
                success: true,
                data: true
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }



    public canMoveToNext(): Promise<RepositoryResult<boolean>> {
        try {
            const stage = this.getStageInfo('active');
            return Promise.resolve({
                success: true,
                data: stage?.canMoveNext ?? false
            });
        } catch (error) {
            return Promise.resolve({
                success: false,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    public canMoveToPrevious(): Promise<RepositoryResult<boolean>> {
        try {
            const stage = this.getStageInfo('active');
            return Promise.resolve({
                success: true,
                data: stage?.canMovePrevious ?? false
            });
        } catch (error) {
            return Promise.resolve({
                success: false,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    public isFirstStage(): Promise<RepositoryResult<boolean>> {
        try {
            const stage = this.getStageInfo('active');
            return Promise.resolve({
                success: true,
                data: stage?.isFirstStage ?? false
            });
        } catch (error) {
            return Promise.resolve({
                success: false,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    public isLastStage(): Promise<RepositoryResult<boolean>> {
        try {
            const stage = this.getStageInfo('active');
            return Promise.resolve({
                success: true,
                data: stage?.isLastStage ?? false
            });
        } catch (error) {
            return Promise.resolve({
                success: false,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    public getEntityInfo(): Promise<RepositoryResult<EntityInfo>> {
        try {
            const xrmWindow = window as XrmWindow;
            const entity = xrmWindow.Xrm?.Page?.data?.entity;

            if (!entity) {
                return Promise.resolve({
                    success: false,
                    error: 'Entity context not available'
                });
            }

            const entityId = entity.getId().replace(/[{}]/g, '');
            const entityName = entity.getEntityName();

            if (!entityId || !entityName) {
                return Promise.resolve({
                    success: false,
                    error: 'Entity information incomplete'
                });
            }

            if (xrmWindow.Xrm?.Utility?.getEntityMetadata) {
                return xrmWindow.Xrm.Utility.getEntityMetadata(entityName)
                    .then((meta) => {
                        const entityInfo: EntityInfo = {
                            entityId,
                            entityLogicalName: entityName,
                            entitySetName: meta?.EntitySetName ?? (entityName.endsWith('s') ? entityName : `${entityName}s`),
                            objectTypeCode: meta?.ObjectTypeCode ?? 1
                        };

                        return {
                            success: true,
                            data: entityInfo
                        };
                    })
                    .catch(() => {
                        const entityInfo: EntityInfo = {
                            entityId,
                            entityLogicalName: entityName,
                            entitySetName: entityName.endsWith('s') ? entityName : `${entityName}s`,
                            objectTypeCode: 1
                        };

                        return {
                            success: true,
                            data: entityInfo
                        };
                    });
            }

            const entityInfo: EntityInfo = {
                entityId,
                entityLogicalName: entityName,
                entitySetName: entityName.endsWith('s') ? entityName : `${entityName}s`,
                objectTypeCode: 1
            };

            return Promise.resolve({
                success: true,
                data: entityInfo
            });
        } catch (error) {
            return Promise.resolve({
                success: false,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }





    private getBpfProcess(): unknown {
        const xrm = window as { Xrm?: { Page?: { data?: { process?: unknown } } } };
        return xrm.Xrm?.Page?.data?.process ?? null;
    }

    private getStageInfo(type: 'active' | 'selected'): BpfStageInfo | null {
        try {
            const process = this.getBpfProcess();
            if (!process) {
                return null;
            }

            const bpf = process as BpfProcess;
            const target = type === 'selected' ? bpf.getSelectedStage() : bpf.getActiveStage();

            if (!target) {
                if (type === 'selected') {
                    return this.getStageInfo('active');
                }
                return null;
            }

            const collection = bpf.getActivePath();
            if (!collection) {
                return null;
            }

            const stages = this.extractStagesFromCollection(collection);
            if (stages.length === 0) {
                return null;
            }

            const index = stages.findIndex((stage: BpfStage) => stage.getId() === target.getId());

            return {
                stageId: target.getId(),
                stageName: target.getName(),
                isFirstStage: index === 0,
                isLastStage: index === stages.length - 1,
                canMoveNext: index < stages.length - 1,
                canMovePrevious: index > 0
            };
        } catch {
            return null;
        }
    }

    private extractStagesFromCollection(collection: BpfCollection): BpfStage[] {
        const stages: BpfStage[] = [];
        
        try {
            if (typeof collection.getAll === 'function') {
                const allStages = collection.getAll();
                if (Array.isArray(allStages)) {
                    return allStages;
                }
            }

            if (typeof collection.forEach === 'function') {
                collection.forEach((stage: BpfStage) => {
                    if (stage) {
                        stages.push(stage);
                    }
                });
                return stages;
            }

            if (typeof collection.getLength === 'function' && typeof collection.getByIndex === 'function') {
                const length = collection.getLength();
                for (let i = 0; i < length; i++) {
                    const stage = collection.getByIndex(i);
                    if (stage) {
                        stages.push(stage);
                    }
                }
                return stages;
            }
        } catch {
            // Ignore errors and continue with empty stages array
        }

        return stages;
    }




}
