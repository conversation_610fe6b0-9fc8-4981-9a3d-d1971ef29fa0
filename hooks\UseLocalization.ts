import { type LocalizationStrings, englishStrings, vietnameseStrings } from '../localization/LocalizationStrings';

export type { LocalizationStrings };

export type SupportedLanguage = 'vi' | 'en';

export const getLocalizedStrings = (language: SupportedLanguage = 'vi'): LocalizationStrings => {
    switch (language) {
        case 'en':
            return englishStrings;
        case 'vi':
            return vietnameseStrings;
        default:
            return vietnameseStrings;
    }
};
