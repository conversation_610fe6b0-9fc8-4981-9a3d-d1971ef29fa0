import type { SupportedLanguage } from '../types/ComponentTypes';
import { englishStrings, vietnameseStrings, type LocalizationStrings } from '../localization/LocalizationStrings';

export type { LocalizationStrings, SupportedLanguage };

export const DEFAULT_LANGUAGE: SupportedLanguage = 'vi';
export const SUPPORTED_LANGUAGES: SupportedLanguage[] = ['vi', 'en'];

export const getLocalizedStrings = (language: SupportedLanguage = DEFAULT_LANGUAGE): LocalizationStrings => {
    switch (language) {
        case 'en':
            return englishStrings;
        case 'vi':
            return vietnameseStrings;
        default:
            return vietnameseStrings;
    }
};
