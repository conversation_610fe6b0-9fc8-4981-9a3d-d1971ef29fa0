import { englishStrings, vietnameseStrings, type LocalizationStrings } from '../localization/LocalizationStrings';
import { DEFAULT_LANGUAGE, type SupportedLanguage } from '../types/LocalizationTypes';

export type { LocalizationStrings, SupportedLanguage };

export const getLocalizedStrings = (language: SupportedLanguage = DEFAULT_LANGUAGE): LocalizationStrings => {
    switch (language) {
        case 'en':
            return englishStrings;
        case 'vi':
            return vietnameseStrings;
        default:
            return vietnameseStrings;
    }
};