import type {
    EntityInfo,
    IBpfRepository,
    ITimelineRepository,
    RepositoryOptions,
    TimelineConfig
} from '../types';
import { DEFAULT_TIMELINE_CONFIG } from '../types';
import { BpfRepository } from './BpfRepository';
import { TimelineRepository } from './TimelineRepository';

export function createBpfRepository(options?: RepositoryOptions): IBpfRepository {
    return new BpfRepository(options);
}

export function createTimelineRepository(
    entityInfo: EntityInfo,
    timelineConfig: TimelineConfig = DEFAULT_TIMELINE_CONFIG,
    options?: RepositoryOptions
): ITimelineRepository {
    return new TimelineRepository(entityInfo, timelineConfig, options);
}