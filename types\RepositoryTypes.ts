import type { BpfActionName, BpfStageInfo } from './BpfTypes';
import type { AttachedFile } from './FileTypes';

export interface RepositoryResult<T = unknown> {
    success: boolean;
    data?: T;
    error?: string;
}

export interface RepositoryOptions {
    timeout?: number;
    retryCount?: number;
}

export interface IBpfRepository {
    getProcessStages(): Promise<RepositoryResult<BpfStageInfo[]>>;
    getCurrentStage(): Promise<RepositoryResult<BpfStageInfo>>;

    moveToNextStage(): Promise<RepositoryResult<boolean>>;
    moveToPreviousStage(): Promise<RepositoryResult<boolean>>;

    canMoveToNext(): Promise<RepositoryResult<boolean>>;
    canMoveToPrevious(): Promise<RepositoryResult<boolean>>;
    isFirstStage(): Promise<RepositoryResult<boolean>>;
    isLastStage(): Promise<RepositoryResult<boolean>>;

    getEntityInfo(): Promise<RepositoryResult<EntityInfo>>;
}

export interface ITimelineRepository {
    createNote(entityId: string, subject: string, noteText: string, attachedFiles?: AttachedFile[]): Promise<RepositoryResult<string>>;
    getNotesBySubject(entityId: string, subjectPattern: string): Promise<RepositoryResult<TimelineNote[]>>;

    createStageNote(stageId: string, comment: string, action: BpfActionName, attachedFiles?: AttachedFile[]): Promise<RepositoryResult<string>>;
    getLatestStageComment(stageId: string): Promise<RepositoryResult<string>>;
    getStageNotes(stageId: string): Promise<RepositoryResult<TimelineNote[]>>;
}

export interface EntityInfo {
    entityId: string;
    entityLogicalName: string;
    entitySetName: string;
    objectTypeCode: number;
    entityDisplayName?: string;
}

export interface TimelineConfig {
    subjectPrefix: string;
}

export const DEFAULT_TIMELINE_CONFIG: TimelineConfig = {
    subjectPrefix: 'PCP'
};

export interface TimelineNote {
    noteId: string;
    subject: string;
    noteText: string;
    createdOn: Date;
    createdBy?: string;
    modifiedOn?: Date;
    modifiedBy?: string;
    attachments?: AttachedFile[];
}

export interface DynamicsNote {
    annotationid: string;
    subject: string;
    notetext: string;
    createdon: string;
    filename?: string;
    filesize?: number;
    mimetype?: string;
    documentbody?: string;
}

export interface DynamicsQueryResult {
    entities: DynamicsNote[];
    value?: DynamicsNote[];
}