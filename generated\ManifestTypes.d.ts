export interface IInputs {
    sourceControl: ComponentFramework.PropertyTypes.StringProperty;
    popupTitle: ComponentF<PERSON>ework.PropertyTypes.StringProperty;
    textLabel: ComponentFramework.PropertyTypes.StringProperty;
    textPlaceHolder: ComponentFramework.PropertyTypes.StringProperty;
    language: ComponentFramework.PropertyTypes.StringProperty;
    enableFileAttachment: ComponentFramework.PropertyTypes.TwoOptionsProperty;
    allowedFileTypes: ComponentFramework.PropertyTypes.StringProperty;
    maxFileSize: ComponentFramework.PropertyTypes.WholeNumberProperty;
}
export interface IOutputs {
    sourceControl?: string;
}
