import type {
    AttachedFile,
    BpfActionName,
    DynamicsNote,
    DynamicsQueryResult,
    EntityInfo,
    ITimelineRepository,
    RepositoryError,
    RepositoryOptions,
    RepositoryResult,
    TimelineConfig,
    TimelineNote
} from '../types';
import { DEFAULT_TIMELINE_CONFIG } from '../types';
import { createRepositoryError } from './ErrorHandler';

interface XrmWindow extends Window {
    Xrm?: {
        WebApi?: {
            createRecord: (entityLogicalName: string, data: Record<string, unknown>) => Promise<{ annotationid: string }>;
            retrieveMultipleRecords: (entityLogicalName: string, options?: string) => Promise<DynamicsQueryResult>;
        };
    };
}

interface NoteData {
    subject: string;
    notetext: string;
    [key: string]: unknown;
}

export class TimelineRepository implements ITimelineRepository {
    public readonly options: RepositoryOptions;
    private entityInfo: EntityInfo;
    private timelineConfig: TimelineConfig;

    constructor(entityInfo: EntityInfo, timelineConfig: TimelineConfig = DEFAULT_TIMELINE_CONFIG, options: RepositoryOptions = {}) {
        this.entityInfo = entityInfo;
        this.timelineConfig = timelineConfig;
        this.options = {
            timeout: 5000,
            retryCount: 3,
            ...options
        };
    }

    public createNote(
        entityId: string,
        subject: string,
        noteText: string,
        attachedFiles?: AttachedFile[]
    ): Promise<RepositoryResult<string>> {
        try {
            const noteData = this.buildNoteData(entityId, subject, noteText, attachedFiles?.[0]);

            return this.executeCreateNote(noteData)
                .then((result) => {
                    if (!result) {
                        return {
                            success: false,
                            error: 'Failed to create note'
                        };
                    }

                    return {
                        success: true,
                        data: result.annotationid
                    };
                })
                .catch((error) => {
                    const repoError = this.handleError(error);
                    return {
                        success: false,
                        error: repoError.message
                    };
                });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }



    public getNotesBySubject(
        entityId: string,
        subjectPattern: string
    ): Promise<RepositoryResult<TimelineNote[]>> {
        try {
            const fetchXml = this.buildSubjectFetchXml(entityId, subjectPattern);

            return this.executeFetchXml(fetchXml)
                .then((result) => {
                    if (!result?.entities || !Array.isArray(result.entities)) {
                        return {
                            success: true,
                            data: []
                        };
                    }

                    const timelineNotes = result.entities.map(note => this.convertToTimelineNote(note));

                    return {
                        success: true,
                        data: timelineNotes
                    };
                })
                .catch((error) => {
                    const repoError = this.handleError(error);
                    return {
                        success: false,
                        error: repoError.message
                    };
                });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }





    // Stage-specific operations
    public createStageNote(
        stageId: string,
        comment: string,
        action: BpfActionName,
        attachedFiles?: AttachedFile[]
    ): Promise<RepositoryResult<string>> {
        try {
            const subject = this.generateStageSubject(stageId, action, attachedFiles?.[0]);
            const noteText = attachedFiles?.length ?
                comment || `File attachment: ${attachedFiles[0].name}` :
                comment;

            return this.createNote(this.entityInfo.entityId, subject, noteText, attachedFiles);
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    public getLatestStageComment(stageId: string): Promise<RepositoryResult<string>> {
        try {
            return this.getStageNotes(stageId)
                .then((stageNotesResult) => {
                    if (!stageNotesResult.success || !stageNotesResult.data || stageNotesResult.data.length === 0) {
                        return {
                            success: true,
                            data: ''
                        };
                    }

                    const latest = stageNotesResult.data.sort((a, b) =>
                        new Date(b.createdOn).getTime() - new Date(a.createdOn).getTime()
                    )[0];

                    return {
                        success: true,
                        data: latest.noteText
                    };
                })
                .catch((error) => {
                    const repoError = this.handleError(error);
                    return {
                        success: false,
                        error: repoError.message
                    };
                });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    public async getStageNotes(stageId: string): Promise<RepositoryResult<TimelineNote[]>> {
        try {
            const shortId = this.getShortStageId(stageId);
            const subjectPattern = `${this.timelineConfig.subjectPrefix}_${shortId}_%`;
            
            return await this.getNotesBySubject(this.entityInfo.entityId, subjectPattern);
        } catch (error) {
            const repoError = this.handleError(error);
            return {
                success: false,
                error: repoError.message
            };
        }
    }



    // Private helper methods
    private buildNoteData(
        entityId: string,
        subject: string,
        noteText: string,
        attachedFile?: AttachedFile
    ): NoteData {
        const noteData: NoteData = {
            subject,
            notetext: noteText,
            [`objectid_${this.entityInfo.entityLogicalName}@odata.bind`]:
                `/${this.entityInfo.entitySetName}(${entityId})`
        };

        // Add file attachment data if provided
        if (attachedFile) {
            noteData.filename = attachedFile.name;
            noteData.documentbody = attachedFile.content;
            noteData.mimetype = attachedFile.type;
            noteData.filesize = attachedFile.size;
        }

        return noteData;
    }

    private generateStageSubject(stageId: string, action: string, attachedFile?: AttachedFile): string {
        const shortId = this.getShortStageId(stageId);
        const baseSubject = `${this.timelineConfig.subjectPrefix}_${shortId}_${action}`;
        
        return attachedFile ? `${baseSubject} - ${attachedFile.name}` : baseSubject;
    }

    private getShortStageId(stageId: string): string {
        return stageId.substring(0, 8);
    }

    private buildSubjectFetchXml(entityId: string, subjectPattern: string): string {
        return `<fetch version="1.0" output-format="xml-platform" mapping="logical" distinct="false">
            <entity name="annotation">
                <attribute name="annotationid" />
                <attribute name="subject" />
                <attribute name="notetext" />
                <attribute name="createdon" />
                <attribute name="filename" />
                <attribute name="filesize" />
                <attribute name="mimetype" />
                <order attribute="createdon" descending="true" />
                <filter type="and">
                    <condition attribute="subject" operator="like" value="${subjectPattern}"/>
                    <condition attribute="objecttypecode" operator="eq" value="${this.entityInfo.objectTypeCode}"/>
                    <condition attribute="objectid" operator="eq" value="${entityId}"/>
                </filter>
            </entity>
        </fetch>`;
    }

    private convertToTimelineNote(note: DynamicsNote): TimelineNote {
        const timelineNote: TimelineNote = {
            noteId: note.annotationid,
            subject: note.subject ?? '',
            noteText: note.notetext ?? '',
            createdOn: new Date(note.createdon ?? Date.now())
        };

        if (note.filename) {
            timelineNote.attachments = [{
                id: note.annotationid,
                name: note.filename,
                size: note.filesize ?? 0,
                type: note.mimetype ?? 'application/octet-stream',
                content: note.documentbody ?? '',
                uploadDate: new Date(note.createdon ?? Date.now())
            }];
        }

        return timelineNote;
    }

    // getEntitySetName() removed - now using this.entityInfo.entitySetName directly

    private async executeCreateNote(noteData: NoteData): Promise<{ annotationid: string } | null> {
        const xrmWindow = window as XrmWindow;
        if (!xrmWindow.Xrm?.WebApi) {
            throw new Error('Xrm.WebApi not available');
        }

        try {
            const result = await xrmWindow.Xrm.WebApi.createRecord('annotation', noteData);
            return result;
        } catch (error) {
            // Re-throw with context for better error handling upstream
            throw new Error(`Failed to create note: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    private async executeFetchXml(fetchXml: string): Promise<DynamicsQueryResult> {
        const xrmWindow = window as XrmWindow;
        if (!xrmWindow.Xrm?.WebApi) {
            throw new Error('Xrm.WebApi not available');
        }

        const encoded = encodeURIComponent(fetchXml);
        return xrmWindow.Xrm.WebApi.retrieveMultipleRecords('annotation', `?fetchXml=${encoded}`);
    }

    public handleError(error: unknown): RepositoryError {
        return createRepositoryError(error, 'Timeline');
    }


}
