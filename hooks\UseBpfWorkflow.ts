import * as React from "react";
import { BpfActionType, TOAST_INTENTS } from '../types';
import type { AttachedFile, BpfActionHandlers, BpfActionName, BpfActionResult, IBpfHelper, ToastIntent } from '../types';
import { getLocalizedStrings, type SupportedLanguage } from './UseLocalization';

interface UseBpfWorkflowProps {
    bpfHelper: IBpfHelper | null;
    onClose: () => void;
    currentComment?: string;
    attachedFiles?: AttachedFile[];
    onCommentSubmitted?: () => void;
    onCancel?: () => void;
    onShowToast?: (intent: ToastIntent, title: string, message?: string) => void;
    language?: SupportedLanguage;
    isReadOnly?: boolean;
}

export const useBpfWorkflow = ({ bpfHelper, onClose, currentComment, attachedFiles, onCommentSubmitted, onCancel, onShowToast, language = 'vi', isReadOnly = false }: UseBpfWorkflowProps) => {
    const [isProcessing, setIsProcessing] = React.useState(false);
    const strings = getLocalizedStrings(language);

    const executeAction = (
        action: () => Promise<boolean>,
        successMsg: string,
        errorMsg: string,
        actionType?: BpfActionName
    ): Promise<BpfActionResult> => {
        setIsProcessing(true);

        if (!bpfHelper) {
            setIsProcessing(false);
            return Promise.resolve({ success: true, message: successMsg });
        }

        const notePromise = (currentComment?.trim() && actionType)
            ? bpfHelper.createStageNote(currentComment.trim(), actionType, attachedFiles)
                .then((created) => {
                    if (!created) {
                        onShowToast?.(TOAST_INTENTS.WARNING, strings.messages.warning.noteCreationWarning, strings.messages.warning.noteCreationWarning);
                    }
                    return created;
                })
                .catch(() => {
                    onShowToast?.(TOAST_INTENTS.ERROR, strings.messages.error.noteCreationFailed, strings.messages.error.noteCreationFailed);
                    return false;
                })
            : Promise.resolve(true);

        return notePromise
            .then(() => action())
            .then((success) => {
                if (success) {
                    onCommentSubmitted?.();
                    onShowToast?.(TOAST_INTENTS.SUCCESS, successMsg);
                } else {
                    onShowToast?.(TOAST_INTENTS.ERROR, strings.messages.error.operationFailed, errorMsg);
                }
                return { success, message: success ? successMsg : errorMsg };
            })
            .catch((error) => {
                const errorMessage = error instanceof Error ? error.message : strings.errors.general.unknownError;
                onShowToast?.(TOAST_INTENTS.ERROR, strings.messages.error.operationFailed, errorMessage);
                return { success: false, error: errorMessage };
            })
            .finally(() => {
                setIsProcessing(false);
            });
    };

    const actionHandlers: BpfActionHandlers = {
        onCancel: () => {
            // In read-only mode, close immediately without confirmation
            if (isReadOnly) {
                onCancel?.();
                onClose();
                return;
            }

            // In editable mode, check if there's content to potentially lose
            if (currentComment?.trim()) {
                const shouldDiscard = window.confirm(
                    strings.messages.confirmation.discardChanges
                );
                if (shouldDiscard) {
                    onCancel?.(); // Clear content only if confirmed
                    onClose();
                }
                // If not confirmed, stay in popup
            } else {
                // No content to lose, close immediately
                onClose();
            }
        },
        onSubmit: () => executeAction(
            () => {
                if (!bpfHelper) {
                    throw new Error(strings.messages.error.bpfNotAvailable);
                }
                return bpfHelper.moveToNextStage();
            },
            strings.messages.success.submitted,
            strings.messages.error.submitFailed,
            BpfActionType.SUBMIT
        ),
        onRecommend: () => executeAction(
            () => {
                if (!bpfHelper) {
                    throw new Error(strings.messages.error.bpfNotAvailable);
                }
                return bpfHelper.moveToNextStage();
            },
            strings.messages.success.recommended,
            strings.messages.error.approveFailed, // Reuse approve error for now
            BpfActionType.RECOMMEND
        ),
        onApprove: () => executeAction(
            () => {
                if (!bpfHelper) {
                    throw new Error(strings.messages.error.bpfNotAvailable);
                }
                return bpfHelper.moveToNextStage();
            },
            strings.messages.success.approved,
            strings.messages.error.approveFailed,
            BpfActionType.APPROVE
        ),
        onReject: () => executeAction(
            () => {
                if (!bpfHelper) {
                    throw new Error(strings.messages.error.bpfNotAvailable);
                }
                return bpfHelper.moveToPreviousStage();
            },
            strings.messages.success.rejected,
            strings.messages.error.rejectFailed,
            BpfActionType.REJECT
        )
    };

    return {
        actionHandlers,
        isProcessing
    };
};