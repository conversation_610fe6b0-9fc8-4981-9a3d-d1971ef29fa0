import type { BpfActionHandlers, BpfButtonConfig, IBpfHelper } from './BpfTypes';
import type { AttachedFile } from './FileTypes';

export type SupportedLanguage = 'vi' | 'en';

export interface ProcessCheckpointProps {
    text: string;
    getTextValue: (value: string) => void;
    popupTitle: string;
    textLabel: string;
    textPlaceHolder: string;
    bpfHelper?: IBpfHelper | null;
    targetStageId?: string;
    language?: SupportedLanguage;
    enableFileAttachment?: boolean;
    allowedFileTypes?: string;
    maxFileSize?: number;
}

export interface CommentModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    value?: string;
    onChange?: (value: string) => void;
    modalTitle: string;
    textLabel: string;
    textPlaceHolder: string;
    bpfActionHandlers?: BpfActionHandlers;
    buttonConfig?: BpfButtonConfig;
    isProcessing?: boolean;
    readOnly?: boolean;
    enableFileAttachment?: boolean;
    allowedFileTypes?: string[];
    maxFileSize?: number;
    attachedFiles?: AttachedFile[];
    onFilesChange?: (files: AttachedFile[]) => void;
}
