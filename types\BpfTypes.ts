import type { AttachedFile } from './FileTypes';

export interface BpfStage {
    getId(): string;
    getName(): string;
}

export interface BpfCollection {
    get(key: string): BpfStage | null;
    getAll(): BpfStage[];
    getByName(name: string): BpfStage | null;
    getByIndex(index: number): BpfStage | null;
    getLength(): number;
    forEach(callback: (stage: BpfStage, index: number) => void): void;
}

export interface BpfProcess {
    getActiveStage(): BpfStage | null;
    getSelectedStage(): BpfStage | null;
    getActivePath(): BpfCollection;
    moveNext(): Promise<void>;
    movePrevious(): Promise<void>;
}

export enum BpfActionType {
    CANCEL = 'cancel',
    SUBMIT = 'submit',
    RECOMMEND = 'recommend',  // Tham mưu - for intermediate stages
    APPROVE = 'approve',      // Duyệt - for final stage only
    REJECT = 'reject'
}

export type BpfActionName = 'submit' | 'recommend' | 'approve' | 'reject';

export interface BpfActionResult {
    success: boolean;
    message?: string;
    error?: string;
}

export interface BpfActionHandlers {
    onCancel: () => void;
    onSubmit?: () => Promise<BpfActionResult>;
    onRecommend?: () => Promise<BpfActionResult>;  // Tham mưu handler
    onApprove?: () => Promise<BpfActionResult>;    // Duyệt handler
    onReject?: () => Promise<BpfActionResult>;     // Từ chối handler
}

export interface BpfStageInfo {
    stageId: string;
    stageName: string;
    isFirstStage: boolean;
    isLastStage: boolean;
    canMoveNext: boolean;
    canMovePrevious: boolean;
}

export interface BpfButtonConfig {
    showCancel: boolean;
    showSubmit: boolean;
    showRecommend: boolean;  // Tham mưu button for intermediate stages
    showApprove: boolean;    // Duyệt button for final stage
    showReject: boolean;
}

export interface IBpfHelper {
    getCurrentStageInfo(): BpfStageInfo | null;
    getSelectedStageInfo(): BpfStageInfo | null;
    getButtonConfig(targetStageId?: string): BpfButtonConfig;
    moveToNextStage(): Promise<boolean>;
    moveToPreviousStage(): Promise<boolean>;
    createStageNote(comment: string, action: BpfActionName, attachedFiles?: AttachedFile[]): Promise<boolean>;
    getLatestStageComment(): Promise<string | null>;
    isCurrentStageReadOnly(): Promise<boolean>;
    getStageComment(stageId: string): Promise<string | null>;
}