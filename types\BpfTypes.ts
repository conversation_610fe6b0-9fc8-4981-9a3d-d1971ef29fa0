import type { AttachedFile } from './FileTypes';

export interface BpfStage {
    getId(): string;
    getName(): string;
}

export interface BpfCollection {
    get(key: string): BpfStage | null;
    getAll(): BpfStage[];
    getByName(name: string): BpfStage | null;
    getByIndex(index: number): BpfStage | null;
    getLength(): number;
    forEach(callback: (stage: BpfStage, index: number) => void): void;
}

export interface BpfProcess {
    getActiveStage(): BpfStage | null;
    getSelectedStage(): BpfStage | null;
    getActivePath(): BpfCollection;
    moveNext(): Promise<void>;
    movePrevious(): Promise<void>;
}

export enum BpfActionType {
    CANCEL = 'cancel',
    SUBMIT = 'submit',
    APPROVE = 'approve',
    REJECT = 'reject'
}

export type BpfActionName = 'submit' | 'approve' | 'reject';

export interface BpfActionResult {
    success: boolean;
    message?: string;
    error?: string;
}

export interface BpfActionHandlers {
    onCancel: () => void;
    onSubmit?: () => Promise<BpfActionResult>;
    onApprove?: () => Promise<BpfActionResult>;
    onReject?: () => Promise<BpfActionResult>;
}

export interface BpfStageInfo {
    stageId: string;
    stageName: string;
    isFirstStage: boolean;
    isLastStage: boolean;
    canMoveNext: boolean;
    canMovePrevious: boolean;
}

export interface BpfButtonConfig {
    showCancel: boolean;
    showSubmit: boolean;
    showApprove: boolean;
    showReject: boolean;
}

export interface IBpfHelper {
    getCurrentStageInfo(): BpfStageInfo | null;
    getSelectedStageInfo(): BpfStageInfo | null;
    getButtonConfig(targetStageId?: string): BpfButtonConfig;
    moveToNextStage(): Promise<boolean>;
    moveToPreviousStage(): Promise<boolean>;
    createStageNote(comment: string, action: BpfActionName, attachedFiles?: AttachedFile[]): Promise<boolean>;
    getLatestStageComment(): Promise<string | null>;
    isCurrentStageReadOnly(): Promise<boolean>;
    getStageComment(stageId: string): Promise<string | null>;
}
