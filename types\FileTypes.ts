export interface AttachedFile {
    id: string;
    name: string;
    size: number;
    type: string;
    content: string;
    uploadDate: Date;
}

export interface FileAttachmentConfig {
    enabled: boolean;
    allowedTypes: string[];
    maxSizeInMB: number;
    maxFiles: number;
}

export const DEFAULT_FILE_CONFIG: FileAttachmentConfig = {
    enabled: false,
    allowedTypes: ['pdf', 'docx', 'xlsx', 'doc', 'xls', 'pptx', 'txt'],
    maxSizeInMB: 10,
    maxFiles: 1
};

export const DEFAULT_FILE_TYPES_STRING = 'pdf,docx,xlsx';

export const parseFileTypes = (fileTypesString?: string): string[] => {
    if (!fileTypesString) return DEFAULT_FILE_CONFIG.allowedTypes;
    return fileTypesString.split(',').map(type => type.trim()).filter(Boolean);
};