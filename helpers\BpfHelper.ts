import { createTimelineRepository } from '../repositories';
import {
    DEFAULT_TIMELINE_CONFIG,
    type AttachedFile,
    type BpfActionName,
    type BpfButtonConfig,
    type BpfCollection,
    type BpfProcess,
    type BpfStage,
    type Bpf<PERSON>tageInfo,
    type EntityInfo,
    type IBpfHelper,
    type ITimelineRepository,
    type RepositoryOptions
} from '../types';

export class BpfHelper implements IBpfHelper {
    private timelineRepository: ITimelineRepository | null = null;
    private options: RepositoryOptions;

    constructor(options?: RepositoryOptions) {
        this.options = options ?? {};

        setTimeout(() => {
            void this.initTimelineRepository();
        }, 100);
    }

    private initTimelineRepository(): Promise<void> {
        return this.buildEntityInfo()
            .then((entityInfo) => {
                this.timelineRepository = entityInfo ?
                    createTimelineRepository(entityInfo, DEFAULT_TIMELINE_CONFIG, this.options) :
                    null;
                return;
            })
            .catch(() => {
                this.timelineRepository = null;
                return;
            });
    }

    private buildEntityInfo(): Promise<EntityInfo | null> {
        const xrmWindow = window as {
            Xrm?: {
                Page?: {
                    data?: {
                        entity?: {
                            getId(): string;
                            getEntityName(): string;
                        }
                    }
                };
                Utility?: {
                    getEntityMetadata: (entityLogicalName: string, attributes?: string[]) => Promise<{
                        EntitySetName?: string;
                        LogicalName?: string;
                        ObjectTypeCode?: number;
                    }>;
                };
            }
        };

        const page = xrmWindow.Xrm?.Page;
        if (!page?.data?.entity) {
            return Promise.resolve(null);
        }

        const entityId = page.data.entity.getId().replace(/[{}]/g, '');
        const entityName = page.data.entity.getEntityName();

        if (!xrmWindow.Xrm?.Utility?.getEntityMetadata) {
            // Fail fast - don't make dangerous assumptions about entity metadata
            return Promise.resolve(null);
        }

        return xrmWindow.Xrm.Utility.getEntityMetadata(entityName)
            .then((meta) => {
                if (!meta?.EntitySetName) {
                    // If metadata is incomplete, fail gracefully
                    return null;
                }

                return {
                    entityId,
                    entityLogicalName: entityName,
                    entitySetName: meta.EntitySetName,
                    objectTypeCode: meta.ObjectTypeCode ?? 0, // Use 0 as safe default
                    subjectPrefix: 'PCP'
                };
            })
            .catch(() => null);
    }

    public getCurrentStageInfo(): BpfStageInfo | null {
        return this.getStageInfo('active');
    }

    public getSelectedStageInfo(): BpfStageInfo | null {
        try {
            const process = this.getBpfProcess();
            if (!process) {
                return null;
            }

            const bpf = process as BpfProcess;
            const selected = bpf.getSelectedStage();

            if (!selected) {
                return null;
            }

            const collection = bpf.getActivePath();
            if (!collection) {
                return null;
            }

            const stages = this.extractStagesFromCollection(collection);
            if (stages.length === 0) {
                return null;
            }

            const index = stages.findIndex((stage: BpfStage) => stage.getId() === selected.getId());

            if (index === -1) {
                return null;
            }

            return {
                stageId: selected.getId(),
                stageName: selected.getName(),
                isFirstStage: index === 0,
                isLastStage: index === stages.length - 1,
                canMoveNext: index < stages.length - 1,
                canMovePrevious: index > 0
            };
        } catch {
            return null;
        }
    }

    public getStageInfoById(targetStageId: string): BpfStageInfo | null {
        try {
            const process = this.getBpfProcess();
            if (!process) {
                return null;
            }

            const bpf = process as BpfProcess;
            const collection = bpf.getActivePath();
            if (!collection) {
                return null;
            }

            const stages = this.extractStagesFromCollection(collection);
            if (stages.length === 0) {
                return null;
            }

            const target = stages.find((stage: BpfStage) => stage.getId() === targetStageId);
            if (!target) {
                return null;
            }

            const index = stages.findIndex((stage: BpfStage) => stage.getId() === targetStageId);

            return {
                stageId: target.getId(),
                stageName: target.getName(),
                isFirstStage: index === 0,
                isLastStage: index === stages.length - 1,
                canMoveNext: index < stages.length - 1,
                canMovePrevious: index > 0
            };
        } catch {
            return null;
        }
    }

    private getStageInfo(type: 'active' | 'selected'): BpfStageInfo | null {
        try {
            const process = this.getBpfProcess();
            if (!process) {
                return null;
            }

            const bpf = process as BpfProcess;
            const target = type === 'selected' ? bpf.getSelectedStage() : bpf.getActiveStage();

            if (!target) {
                if (type === 'selected') {
                    return this.getStageInfo('active');
                }
                return null;
            }

            const collection = bpf.getActivePath();
            if (!collection) {
                return null;
            }

            const stages = this.extractStagesFromCollection(collection);
            if (stages.length === 0) {
                return null;
            }

            const index = stages.findIndex((stage: BpfStage) => stage.getId() === target.getId());

            return {
                stageId: target.getId(),
                stageName: target.getName(),
                isFirstStage: index === 0,
                isLastStage: index === stages.length - 1,
                canMoveNext: index < stages.length - 1,
                canMovePrevious: index > 0
            };
        } catch {
            return null;
        }
    }

    private getBpfProcess(): unknown {
        const xrm = window as { Xrm?: { Page?: { data?: { process?: unknown } } } };
        return xrm.Xrm?.Page?.data?.process ?? null;
    }

    public getButtonConfig(targetStageId?: string): BpfButtonConfig {
        const selected = this.getSelectedStageInfo();
        const active = this.getCurrentStageInfo();

        let stage: BpfStageInfo | null = null;
        let isReadOnly = false;

        if (targetStageId) {
            stage = this.getStageInfoById(targetStageId);
            const isTargetActive = active && targetStageId === active.stageId;
            if (!isTargetActive) {
                isReadOnly = true;
            }
        } else {
            if (selected && active) {
                const isSelectedActive = selected.stageId === active.stageId;
                if (isSelectedActive) {
                    stage = active;
                    isReadOnly = false;
                } else {
                    stage = selected;
                    isReadOnly = true;
                }
            } else {
                stage = active;
                isReadOnly = false;
            }
        }

        if (isReadOnly) {
            return {
                showCancel: true,
                showSubmit: false,
                showApprove: false,
                showReject: false
            };
        }

        stage ??= this.getCurrentStageInfo();

        if (!stage) {
            return {
                showCancel: true,
                showSubmit: true,
                showApprove: false,
                showReject: false
            };
        }

        if (stage.isFirstStage) {
            return {
                showCancel: true,
                showSubmit: true,
                showApprove: false,
                showReject: false
            };
        } else {
            return {
                showCancel: true,
                showSubmit: false,
                showApprove: true,
                showReject: true
            };
        }
    }

    public moveToNextStage(): Promise<boolean> {
        const process = this.getBpfProcess();
        if (!process) {
            return Promise.resolve(false);
        }

        const stage = this.getCurrentStageInfo();
        if (!stage?.canMoveNext) {
            return Promise.resolve(false);
        }

        const bpf = process as BpfProcess;
        return bpf.moveNext()
            .then(() => true)
            .catch(() => false);
    }

    public moveToPreviousStage(): Promise<boolean> {
        const process = this.getBpfProcess();
        if (!process) {
            return Promise.resolve(false);
        }

        const stage = this.getCurrentStageInfo();
        if (!stage?.canMovePrevious) {
            return Promise.resolve(false);
        }

        const bpf = process as BpfProcess;
        return bpf.movePrevious()
            .then(() => true)
            .catch(() => false);
    }

    /**
     * Extract stages from BPF collection using primary strategy
     * Fail fast if BPF API is not supported
     */
    private extractStagesFromCollection(collection: BpfCollection): BpfStage[] {
        // Use primary strategy: getAll() method
        if (typeof collection.getAll === 'function') {
            try {
                const allStages = collection.getAll();
                if (Array.isArray(allStages)) {
                    return allStages;
                }
            } catch {
                // Primary strategy failed, return empty array
            }
        }

        // If primary strategy fails, return empty array
        // This is better than complex fallback logic that may not work correctly
        return [];
    }

    /**
     * Create a note in timeline for current stage with optional file attachments
     */
    public createStageNote(comment: string, action: BpfActionName, attachedFiles?: AttachedFile[]): Promise<boolean> {
        if (!this.timelineRepository) {
            return Promise.resolve(false);
        }

        const stage = this.getCurrentStageInfo();
        if (!stage) {
            return Promise.resolve(false);
        }

        return this.timelineRepository.createStageNote(stage.stageId, comment, action, attachedFiles)
            .then((result) => result.success)
            .catch(() => false);
    }

    public getLatestStageComment(): Promise<string | null> {
        if (!this.timelineRepository) {
            return Promise.resolve(null);
        }

        const stage = this.getCurrentStageInfo();
        if (!stage) {
            return Promise.resolve(null);
        }

        return this.timelineRepository.getLatestStageComment(stage.stageId)
            .then((result) => result.success ? result.data ?? null : null)
            .catch(() => null);
    }

    public isCurrentStageReadOnly(): Promise<boolean> {
        if (!this.timelineRepository) {
            return Promise.resolve(false);
        }

        const stage = this.getCurrentStageInfo();
        if (!stage) {
            return Promise.resolve(false);
        }

        return this.timelineRepository.getStageNotes(stage.stageId)
            .then((result) => result.success && result.data ? result.data.length > 0 : false)
            .catch(() => false);
    }

    public getStageComment(stageId: string): Promise<string | null> {
        if (!this.timelineRepository) {
            return this.initTimelineRepository()
                .then(() => this.getStageCommentInternal(stageId));
        }

        return this.getStageCommentInternal(stageId);
    }

    private getStageCommentInternal(stageId: string): Promise<string | null> {
        if (!this.timelineRepository) {
            return Promise.resolve(null);
        }

        return this.timelineRepository.getLatestStageComment(stageId)
            .then((result) => result.success ? result.data ?? null : null)
            .catch(() => null);
    }

    /**
     * Check if a stage is the currently selected stage
     */
    public isStageSelected(stageId: string): boolean {
        const stageInfo = this.getSelectedStageInfo();
        return stageInfo?.stageId === stageId;
    }

}
