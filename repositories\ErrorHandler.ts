import type { RepositoryError } from '../types';
import { RepositoryErrorType } from '../types';

/**
 * Create a standardized repository error from any error type
 */
export const createRepositoryError = (error: unknown, context: string): RepositoryError => {
    const timestamp = new Date();
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorType = classifyErrorType(errorMessage);
    
    return {
        type: errorType,
        message: createErrorMessage(errorType, context),
        details: error,
        timestamp
    };
};

/**
 * Classify error type based on error message patterns
 */
const classifyErrorType = (errorMessage: string): RepositoryErrorType => {
    const lowerMessage = errorMessage.toLowerCase();

    if (lowerMessage.includes('timeout') || lowerMessage.includes('timed out')) {
        return RepositoryErrorType.TIMEOUT;
    }

    if (lowerMessage.includes('permission') || 
        lowerMessage.includes('access') || 
        lowerMessage.includes('unauthorized') ||
        lowerMessage.includes('forbidden')) {
        return RepositoryErrorType.PERMISSION_ERROR;
    }

    if (lowerMessage.includes('not found') || 
        lowerMessage.includes('404') ||
        lowerMessage.includes('does not exist')) {
        return RepositoryErrorType.NOT_FOUND;
    }

    if (lowerMessage.includes('network') || 
        lowerMessage.includes('fetch') ||
        lowerMessage.includes('connection') ||
        lowerMessage.includes('offline')) {
        return RepositoryErrorType.NETWORK_ERROR;
    }

    if (lowerMessage.includes('validation') ||
        lowerMessage.includes('invalid') ||
        lowerMessage.includes('bad request') ||
        lowerMessage.includes('400')) {
        return RepositoryErrorType.VALIDATION_ERROR;
    }

    return RepositoryErrorType.UNKNOWN;
};

/**
 * Create user-friendly error message based on type and context
 */
const createErrorMessage = (type: RepositoryErrorType, context: string): string => {
    switch (type) {
        case RepositoryErrorType.TIMEOUT:
            return `${context} operation timed out`;
        case RepositoryErrorType.PERMISSION_ERROR:
            return `Permission denied for ${context} operation`;
        case RepositoryErrorType.NOT_FOUND:
            return `${context} resource not found`;
        case RepositoryErrorType.NETWORK_ERROR:
            return `Network error during ${context} operation`;
        case RepositoryErrorType.VALIDATION_ERROR:
            return `Validation error in ${context} operation`;
        default:
            return `Unknown error occurred during ${context} operation`;
    }
};

/**
 * Check if error is retryable based on error type
 */
export const isRetryableError = (error: RepositoryError): boolean => {
    return error.type === RepositoryErrorType.TIMEOUT ||
           error.type === RepositoryErrorType.NETWORK_ERROR;
};

/**
 * Check if error should be shown to user
 */
export const shouldShowErrorToUser = (error: RepositoryError): boolean => {
    // Don't show technical errors to users
    return error.type !== RepositoryErrorType.UNKNOWN;
};
