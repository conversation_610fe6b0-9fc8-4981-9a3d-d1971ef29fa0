import * as React from "react";
import {
    MessageBar,
    MessageBarBody,
    MessageBarTitle,
    Button
} from "@fluentui/react-components";
import { getLocalizedStrings, type SupportedLanguage } from '../hooks/UseLocalization';
import { getValidatedLanguage } from '../types/LocalizationTypes';

interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
    errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
    children: React.ReactNode;
    fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
    language?: SupportedLanguage;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return {
            hasError: true,
            error
        };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        // Silent error handling - store error info for display
        this.setState({
            error,
            errorInfo
        });
    }

    handleRetry = () => {
        this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    };

    render() {
        if (this.state.hasError) {
            if (this.props.fallback && this.state.error) {
                const FallbackComponent = this.props.fallback;
                return <FallbackComponent error={this.state.error} retry={this.handleRetry} />;
            }

            const strings = getLocalizedStrings(getValidatedLanguage(this.props.language));

            return (
                <div style={{ padding: '16px', maxWidth: '500px' }}>
                    <MessageBar intent="error">
                        <MessageBarBody>
                            <MessageBarTitle>{strings.errors.errorBoundary.title}</MessageBarTitle>
                            {strings.errors.errorBoundary.description}
                            <div style={{ marginTop: '12px' }}>
                                <Button
                                    appearance="primary"
                                    size="small"
                                    onClick={this.handleRetry}
                                >
                                    {strings.ui.buttons.tryAgain}
                                </Button>
                            </div>
                            {this.state.error && (
                                <details style={{ marginTop: '12px', fontSize: '12px' }}>
                                    <summary>{strings.ui.labels.errorDetails}</summary>
                                    <pre style={{
                                        whiteSpace: 'pre-wrap',
                                        backgroundColor: '#f5f5f5',
                                        padding: '8px',
                                        borderRadius: '4px',
                                        marginTop: '8px'
                                    }}>
                                        {this.state.error.message}
                                        {this.state.error.stack}
                                    </pre>
                                </details>
                            )}
                        </MessageBarBody>
                    </MessageBar>
                </div>
            );
        }

        return this.props.children;
    }
}

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
    Component: React.ComponentType<P>,
    fallback?: React.ComponentType<{ error: Error; retry: () => void }>,
    language?: SupportedLanguage
) => {
    const WrappedComponent = (props: P) => (
        <ErrorBoundary fallback={fallback} language={language}>
            <Component {...props} />
        </ErrorBoundary>
    );

    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName ?? Component.name ?? 'Component'})`;
    return WrappedComponent;
};
